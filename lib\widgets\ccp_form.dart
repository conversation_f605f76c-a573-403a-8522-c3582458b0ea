import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import '../l10n/app_localizations.dart';

import '../models/ccp_account.dart';
import '../providers/ccp_accounts_provider.dart';
import '../providers/app_state_provider.dart';
import '../providers/expansion_provider.dart';
import '../screens/save_account_screen.dart';
import '../utils/ccp_calculator.dart';

class CCPForm extends StatefulWidget {
  final Function(CCPAccount) onCalculate;
  final Function(bool) onFocusChange;
  final GlobalKey? keyCcpInput;
  final GlobalKey? keySaveButton;
  final GlobalKey? keyResultsSection;

  const CCPForm({
    super.key,
    required this.onCalculate,
    required this.onFocusChange,
    this.keyCcpInput,
    this.keySaveButton,
    this.keyResultsSection,
  });

  @override
  State<CCPForm> createState() => _CCPFormState();
}

class _CCPFormState extends State<CCPForm> {
  final _formKey = GlobalKey<FormState>();
  final _ccpNumberController = TextEditingController();
  final _ccpNumberFocusNode = FocusNode();

  String? _ccpKey;
  String? _ripKey;
  String? _ripCode;
  Timer? _debounceTimer;

  @override
  void initState() {
    super.initState();
    _ccpNumberFocusNode.addListener(() {
      widget.onFocusChange(_ccpNumberFocusNode.hasFocus);
    });
    _ccpNumberController.addListener(_autoCalculate);

    // Restaurer les données temporaires si l'app revient du background
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _restoreTemporaryData();
    });
  }

  @override
  void dispose() {
    // Sauvegarder les données temporaires avant de disposer
    _saveTemporaryData();

    // Annuler le timer pour éviter les fuites mémoire
    _debounceTimer?.cancel();

    _ccpNumberController.dispose();
    _ccpNumberFocusNode.dispose();
    super.dispose();
  }

  // Restaurer les données temporaires
  void _restoreTemporaryData() {
    final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
    if (appStateProvider.shouldRestoreData()) {
      final data = appStateProvider.restoreCcpData();
      if (data['ccpInput'] != null && data['ccpInput']!.isNotEmpty) {
        _ccpNumberController.text = data['ccpInput']!;
        if (data['calculatedRip'] != null) {
          setState(() {
            _ripCode = data['calculatedRip'];
          });
        }
        debugPrint('Données CCP restaurées: ${data['ccpInput']}');
      }
    }
  }

  // Sauvegarder les données temporaires
  void _saveTemporaryData() {
    final appStateProvider = Provider.of<AppStateProvider>(context, listen: false);
    appStateProvider.saveTempCcpData(_ccpNumberController.text, _ripCode);
  }

  void _autoCalculate() {
    // Annuler le timer précédent pour éviter trop de calculs
    _debounceTimer?.cancel();

    // Utiliser un debounce de 300ms pour optimiser les performances
    _debounceTimer = Timer(const Duration(milliseconds: 300), () {
      _performCalculation();
    });
  }

  void _performCalculation() {
    final ccpNumber = _ccpNumberController.text.trim();
    if (ccpNumber.length >= 6 && CCPCalculator.isValidCCPNumber(ccpNumber)) {
      try {
        final ccpKey = CCPCalculator.calculateCCPKey(ccpNumber);
        final ripKey = CCPCalculator.calculateRIPKey(ccpNumber);
        final ripCode = CCPCalculator.generateRIPCode(ccpNumber);
        if (mounted) {
          setState(() {
            _ccpKey = ccpKey.toString().padLeft(2, '0');
            _ripKey = ripKey.toString().padLeft(2, '0');
            _ripCode = ripCode;
          });

          // Sauvegarder automatiquement les données temporaires
          _saveTemporaryData();
        }
        final account = CCPAccount(
          ccpNumber: ccpNumber,
          ownerName: "",
          ccpKey: ccpKey,
          ripKey: ripKey,
          ripCode: ripCode,
        );
        widget.onCalculate(account);
      } catch (e) {
        // Ignore
      }
    } else {
      if (mounted) {
        setState(() {
          _ccpKey = null;
          _ripKey = null;
          _ripCode = null;
        });
      }
    }
  }

  Future<void> _navigateToSaveScreen() async {
    if (_ccpKey == null || _ripKey == null || _ripCode == null) return;

    final ccpNumber = _ccpNumberController.text.trim();
    final loc = AppLocalizations.of(context);

    // Vérifier si le compte existe déjà
    final accountsProvider = Provider.of<CCPAccountsProvider>(context, listen: false);
    final accountExists = accountsProvider.accounts.any((account) => account.ccpNumber == ccpNumber);

    if (accountExists) {
      final errorColor = const Color(0xFFD32F2F);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.error_outline, color: Colors.white),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  loc.ccpFormAccountExistsError,
                  style: const TextStyle(fontSize: 14),
                ),
              ),
            ],
          ),
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
          backgroundColor: errorColor,
          duration: const Duration(seconds: 3),
        ),
      );
      return;
    }

    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => SaveAccountScreen(
          ccpNumber: ccpNumber,
          ccpKey: int.parse(_ccpKey!),
          ripKey: int.parse(_ripKey!),
          ripCode: _ripCode!,
        ),
      ),
    );

    if (result == true) {
      // Compte sauvegardé avec succès
      _ccpNumberController.clear();
      setState(() {
        _ccpKey = null;
        _ripKey = null;
        _ripCode = null;
      });

      // Afficher un message de succès
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              const Icon(Icons.check_circle, color: Colors.white),
              const SizedBox(width: 8),
              const Text('Compte sauvegardé avec succès'),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
          ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final loc = AppLocalizations.of(context);

    return Form(
      key: _formKey,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.stretch,
        children: [
          // Champ de saisie du numéro CCP
          Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Theme.of(context).colorScheme.primary.withAlpha(25),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextFormField(
              controller: _ccpNumberController,
              focusNode: _ccpNumberFocusNode,
              textDirection: TextDirection.ltr,
              textAlign: Localizations.localeOf(context).languageCode == 'ar'
                  ? TextAlign.right
                  : TextAlign.left,
              decoration: InputDecoration(
                labelText: loc.ccpFormInputLabel,
                hintText: loc.ccpFormInputHint,
                prefixIcon: const Icon(Icons.account_balance_outlined),
                suffixIcon: _ccpNumberController.text.isNotEmpty
                    ? IconButton(
                        icon: const Icon(Icons.clear),
                        tooltip: loc.ccpFormClearTooltip,
                        onPressed: () {
                          _ccpNumberController.clear();
                        },
                      )
                    : null,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                filled: true,
                fillColor: const Color(0xFFFFEC33),
                alignLabelWithHint: true,
                floatingLabelAlignment: FloatingLabelAlignment.start,
              ),
              keyboardType: TextInputType.number,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
                letterSpacing: 1.2,
                color: Colors.black87,
              ),
              inputFormatters: [
                FilteringTextInputFormatter.digitsOnly,
                LengthLimitingTextInputFormatter(10),
              ],
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return loc.ccpFormValidationEmpty;
                }
                if (!CCPCalculator.isValidCCPNumber(value)) {
                  return loc.ccpFormValidationInvalid;
                }
                return null;
              },
            ),
          ),

          // Afficher l'instruction seulement si aucun résultat n'est affiché
          if (_ccpKey == null || _ripKey == null || _ripCode == null) ...[
            const SizedBox(height: 16),

            // Instruction sur la saisie du code CCP
            Container(
              margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 3),
              padding: const EdgeInsets.symmetric(vertical: 5, horizontal: 10),
              decoration: BoxDecoration(
                color: const Color(0xFFFFEC33).withAlpha(20),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: const Color(0xFFFFEC33).withAlpha(50),
                  width: 1,
                ),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Colors.black87,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      loc.ccpFormInputInstruction,
                      style: const TextStyle(
                        color: Colors.black87,
                        fontSize: 11,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],

          // Affichage des résultats selon l'image
          if (_ccpKey != null && _ripKey != null && _ripCode != null) ...[
            const SizedBox(height: 16),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFFF5F5DC), // Beige clair comme dans l'image
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.grey.withAlpha(76),
                  width: 1,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // En-tête avec icône de succès - Deux lignes
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Container(
                        padding: const EdgeInsets.all(3),
                        decoration: const BoxDecoration(
                          color: Colors.green,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 12,
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              loc.ccpCalculationSuccess,
                              style: const TextStyle(
                                fontSize: 13,
                                fontWeight: FontWeight.bold,
                                color: Colors.black87,
                              ),
                            ),
                            Text(
                              loc.ccpCalculationReady,
                              style: const TextStyle(
                                fontSize: 11,
                                fontWeight: FontWeight.w500,
                                color: Colors.black54,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 12),

                  // Ligne de séparation
                  Container(
                    height: 1,
                    color: Colors.grey.withAlpha(76),
                  ),

                  const SizedBox(height: 10),

                  // Clé CCP
                  _buildCompactInfoItem(
                    icon: Icons.vpn_key,
                    label: loc.ccpKeyCcp,
                    value: _ccpKey!,
                  ),

                  const SizedBox(height: 8),

                  // Clé RIP
                  _buildCompactInfoItem(
                    icon: Icons.vpn_key,
                    label: loc.ccpKeyRip,
                    value: _ripKey!,
                  ),

                  const SizedBox(height: 10),

                  // Compte RIP - sur une seule ligne
                  Row(
                    children: [
                      const Icon(
                        Icons.account_balance,
                        size: 16,
                        color: Colors.black87,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        loc.ccpAccountRip,
                        style: const TextStyle(
                          fontSize: 12,
                          fontWeight: FontWeight.w500,
                          color: Colors.black87,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Code RIP avec bouton Copier - Version compacte
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFFFFEC33),
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: Colors.grey.withAlpha(76),
                        width: 1,
                      ),
                    ),
                    child: Column(
                      children: [
                        FittedBox(
                          fit: BoxFit.scaleDown,
                          child: Text(
                            _ripCode!,
                            textDirection: TextDirection.ltr,
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.black87,
                              fontFamily: 'monospace',
                              letterSpacing: 0.5,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 1,
                          ),
                        ),
                        const SizedBox(height: 8),
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton.icon(
                            onPressed: () {
                              final textToCopy = _ripCode!.replaceAll(' ', '');
                              Clipboard.setData(ClipboardData(text: textToCopy));
                              ScaffoldMessenger.of(context).showSnackBar(
                                SnackBar(
                                  content: Row(
                                    children: [
                                      const Icon(Icons.check_circle, color: Colors.white, size: 16),
                                      const SizedBox(width: 6),
                                      Text(loc.ccpCopiedMessage, style: const TextStyle(fontSize: 12)),
                                    ],
                                  ),
                                  backgroundColor: Colors.green,
                                  behavior: SnackBarBehavior.floating,
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(6),
                                  ),
                                  duration: const Duration(seconds: 1),
                                ),
                              );
                            },
                            icon: const Icon(Icons.content_copy, size: 14),
                            label: Text(
                              loc.ccpCopyButton,
                              style: const TextStyle(fontSize: 12),
                            ),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: const Color(0xFFFFEC33),
                              foregroundColor: Colors.black87,
                              elevation: 1,
                              padding: const EdgeInsets.symmetric(vertical: 6),
                              minimumSize: const Size(0, 32),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(4),
                                side: BorderSide(
                                  color: Colors.grey.withAlpha(76),
                                  width: 1,
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  const SizedBox(height: 12),

                  // Bouton ENREGISTRER LE COMPTE - Version agrandie
                  SizedBox(
                    width: double.infinity,
                    child: ElevatedButton.icon(
                      onPressed: _navigateToSaveScreen,
                      icon: const Icon(Icons.save_outlined, color: Colors.white, size: 18),
                      label: Text(
                        loc.ccpSaveButton,
                        style: const TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: const Color(0xFF2196F3), // Bleu comme dans l'image
                        padding: const EdgeInsets.symmetric(vertical: 14),
                        minimumSize: const Size(0, 48),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                        elevation: 2,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildCompactInfoItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          size: 14,
          color: Colors.black87,
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        const Spacer(),
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: const Color(0xFFFFEC33),
            borderRadius: BorderRadius.circular(4),
            border: Border.all(
              color: Colors.grey.withAlpha(76),
              width: 1,
            ),
          ),
          child: Text(
            value,
            textDirection: TextDirection.ltr,
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: Colors.black87,
            ),
          ),
        ),
      ],
    );
  }
}
